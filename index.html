<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Office Ordering System</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <div id="name-input-section">
            <h2>Welcome! What's your name?</h2>
            <input type="text" id="user-name-input" placeholder="Enter your name">
            <button id="save-name-btn">Save Name</button>
        </div>

        <div id="order-form-section" style="display: none;">
            <h2 id="form-title">What do you want?</h2>
            <p>Hello, <strong id="display-name"></strong>!</p>
            <form id="order-form">
                <div class="order-items-container">
                    <div class="items-header">
                        <h3>Order Items</h3>
                        <small class="description">Add multiple items to your order</small>
                    </div>

                    <div class="items-table-header">
                        <div class="header-item">Item</div>
                        <div class="header-quantity">Quantity</div>
                        <div class="header-actions">Actions</div>
                    </div>

                    <div id="order-items" class="order-items">
                        <!-- Dynamic rows will be added here -->
                    </div>

                    <div class="add-item-container">
                        <button type="button" id="add-item-btn" class="add-btn">
                            <span class="plus-icon">+</span> Add Item
                        </button>
                    </div>
                </div>

                <div class="total-section">
                    <div class="total-display">
                        <label for="total-amount">Total Amount Given:</label>
                        <div class="total-amount-container">
                            <span class="currency">Rs</span>
                            <input type="number" id="total-amount" placeholder="Enter amount given to Kamran" min="0" step="0.01" required>
                        </div>
                    </div>
                    <small class="total-description">Enter the total amount you're giving to Kamran (e.g., 500)</small>
                </div>

                <div class="form-actions">
                    <button type="submit" class="submit-btn">Submit Order</button>
                    <button type="button" id="clear-all-btn" class="clear-btn">Clear All</button>
                </div>
            </form>
            <div id="status-message" class="status"></div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
